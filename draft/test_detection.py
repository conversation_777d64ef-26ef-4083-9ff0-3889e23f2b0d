"""
NSFW Image Detection Test Script
Based on the README.md file in model_path/nsfw_detection/

This script implements three different approaches for NSFW image classification:
1. Transformers Pipeline approach (high-level)
2. Direct model loading approach (low-level)
3. YOLO approach using ONNX runtime

Author: Generated by Augment Agent
"""

import os
import sys
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import transformers components
try:
    from transformers import pipeline, AutoModelForImageClassification, ViTImageProcessor
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers library not available. Install with: pip install transformers")
    TRANSFORMERS_AVAILABLE = False

# Import ONNX runtime for YOLO approach
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    print("Warning: onnxruntime not available. Install with: pip install onnxruntime")
    ONNX_AVAILABLE = False


class NSFWDetector:
    """
    A comprehensive NSFW detection class that supports multiple model approaches.
    """

    def __init__(self, model_path="model_path/nsfw_detection"):
        """
        Initialize the NSFW detector.

        Args:
            model_path (str): Path to the model directory
        """
        self.model_path = Path(model_path)
        self.labels_path = self.model_path / "labels.json"

        # Load labels
        self.labels = self._load_labels()

        # Initialize models (lazy loading)
        self.pipeline_classifier = None
        self.direct_model = None
        self.direct_processor = None
        self.onnx_session = None

    def _load_labels(self):
        """Load class labels from JSON file."""
        try:
            with open(self.labels_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: Labels file not found at {self.labels_path}")
            return {"0": "normal", "1": "nsfw"}  # Default labels

    def _load_image(self, image_path):
        """
        Load and validate image file.

        Args:
            image_path (str): Path to image file

        Returns:
            PIL.Image: Loaded image in RGB format
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        try:
            image = Image.open(image_path).convert("RGB")
            return image
        except Exception as e:
            raise ValueError(f"Error loading image {image_path}: {e}")

    def predict_with_pipeline(self, image_path):
        """
        Method 1: Use transformers pipeline for classification (high-level approach).

        Args:
            image_path (str): Path to image file

        Returns:
            dict: Prediction results with confidence scores
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for pipeline approach")

        # Lazy load pipeline
        if self.pipeline_classifier is None:
            print("Loading pipeline classifier...")
            self.pipeline_classifier = pipeline(
                "image-classification",
                model=str(self.model_path)
            )

        # Load and classify image
        img = self._load_image(image_path)
        results = self.pipeline_classifier(img)

        return {
            'method': 'pipeline',
            'predictions': results,
            'top_prediction': results[0] if results else None
        }

    def predict_with_direct_model(self, image_path):
        """
        Method 2: Use direct model loading approach (low-level approach).

        Args:
            image_path (str): Path to image file

        Returns:
            dict: Prediction results with confidence scores
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for direct model approach")

        # Lazy load model and processor
        if self.direct_model is None or self.direct_processor is None:
            print("Loading direct model and processor...")
            self.direct_model = AutoModelForImageClassification.from_pretrained(str(self.model_path))
            self.direct_processor = ViTImageProcessor.from_pretrained(str(self.model_path))

        # Load and process image
        img = self._load_image(image_path)

        with torch.no_grad():
            inputs = self.direct_processor(images=img, return_tensors="pt")
            outputs = self.direct_model(**inputs)
            logits = outputs.logits

            # Get probabilities
            probabilities = torch.nn.functional.softmax(logits, dim=-1)
            predicted_label_idx = logits.argmax(-1).item()
            predicted_label = self.direct_model.config.id2label[predicted_label_idx]
            confidence = probabilities[0][predicted_label_idx].item()

        return {
            'method': 'direct_model',
            'predicted_label': predicted_label,
            'predicted_index': predicted_label_idx,
            'confidence': confidence,
            'all_probabilities': {
                self.direct_model.config.id2label[i]: prob.item()
                for i, prob in enumerate(probabilities[0])
            }
        }